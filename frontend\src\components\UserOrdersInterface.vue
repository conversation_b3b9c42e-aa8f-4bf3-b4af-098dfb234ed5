<template>
  <div class="orders-interface">
    <!-- Header row with sorting -->
    <div class="orders-header">
      <div class="header-spacer"></div>
      <div class="header-size" @click="onClickSort('size')" :class="getSortClass('size')">
        SIZE
        <q-icon :name="getSortIcon('size')" size="xs" />
      </div>
      <div class="header-price" @click="onClickSort('price')" :class="getSortClass('price')">
        <span class="price-symbol">¢</span>
        <q-icon :name="getSortIcon('price')" size="xs" />
      </div>
      <div class="header-action"></div>
    </div>

    <!-- Loading state -->
    <div v-if="isLoading" class="loading-container">
      <q-spinner size="md" />
    </div>

    <!-- Order groups -->
    <div v-else class="orders-container">
      <div v-for="group in sortedOrderGroups" :key="group.eventId" class="order-group">
        <!-- Event container: flex row with icon and data -->
        <div class="event-container">
          <!-- Event icon (left side) -->
          <div class="event-icon">
            <q-avatar size="40px">
              <img :src="group.eventIcon || '/img/default_event.webp'" :alt="group.eventTitle" />
            </q-avatar>
          </div>

          <!-- Data container: flex column with title and order rows -->
          <div class="data-container">
            <!-- Event title -->
            <div class="event-title">
              {{ group.eventTitle }}
            </div>

            <!-- Order rows -->
            <div v-for="order in group.orders" :key="order.id" class="order-container">
              <!-- Market title (only if not blank) -->
              <div v-if="order.marketTitle" class="cell-title">{{ order.marketTitle }}</div>

              <!-- Order row -->
              <div class="order-row">
                <div class="cell-side">{{ order.sideText }}</div>
                <div class="cell-filled">{{ formatDecimal(Number(order.size_matched), 1) }}</div>
                <div class="cell-separator">/</div>
                <div class="cell-amount">{{ formatDecimal(Number(order.original_size), 1) }}</div>
                <div class="cell-at">@</div>
                <div class="cell-price">{{ formatCents(Number(order.price), 1) }}</div>
                <div class="cell-action">
                  <q-btn
                    icon="close"
                    size="xs"
                    flat
                    round
                    class="text-negative"
                    @click="onClickCancelOrder(order)"
                    :title="`Cancel order`"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { PolyClobOpenOrder, PolyGammaMarket } from "@shared/api-dataclasses-shared";
import { formatCents, formatDecimal } from "src/utils";
import { useApi } from "src/api";
import { useOrderStore } from "src/stores/order-store";
import { useNotifier } from "src/notifier";

interface OrderWithMarketInfo extends PolyClobOpenOrder {
  marketTitle?: string;
  eventTitle?: string;
  eventIcon?: string;
  eventId?: string;
  sideText: string;
}

interface OrderGroup {
  eventId: string;
  eventTitle: string;
  eventIcon?: string;
  orders: OrderWithMarketInfo[];
  totalSize: number;
  highestPrice: number;
}

const props = defineProps<{
  userProxyWallet: string;
}>();

const api = useApi();
const orderStore = useOrderStore();
const notifier = useNotifier();

const isLoading = ref(false);
const orders = ref<OrderWithMarketInfo[]>([]);
const sortBy = ref("size");
const sortDesc = ref(true);

const sortedOrderGroups = computed(() => {
  if (!orders.value.length) return [];

  //Group orders by event
  const groupsMap = new Map<string, OrderGroup>();

  for (const order of orders.value) {
    const eventId = order.eventId || 'unknown';

    if (!groupsMap.has(eventId)) {
      groupsMap.set(eventId, {
        eventId,
        eventTitle: order.eventTitle || 'Unknown Event',
        eventIcon: order.eventIcon,
        orders: [],
        totalSize: 0,
        highestPrice: 0
      });
    }

    const group = groupsMap.get(eventId)!;
    group.orders.push(order);
    group.totalSize += Number(order.original_size) || 0;
    group.highestPrice = Math.max(group.highestPrice, Number(order.price) || 0);
  }

  //Convert to array and sort
  const groups = Array.from(groupsMap.values());

  return groups.sort((a, b) => {
    let aVal: number, bVal: number;

    if (sortBy.value === "size") {
      aVal = a.totalSize;
      bVal = b.totalSize;
    }
    else { //price
      aVal = a.highestPrice;
      bVal = b.highestPrice;
    }

    if (aVal < bVal) return sortDesc.value ? 1 : -1;
    if (aVal > bVal) return sortDesc.value ? -1 : 1;
    return 0;
  });
});

async function loadOrders() {
  if (!props.userProxyWallet) return;

  isLoading.value = true;

  try {
    //Get open orders
    const openOrders = await api.getOrders();

    if (!openOrders.length) {
      orders.value = [];
      return;
    }

    //Get unique condition IDs for market lookup
    const conditionIds = [...new Set(openOrders.map(order => order.market))];

    //Get market data for all condition IDs
    const markets = await api.getMarkets(undefined, conditionIds);

    //Create lookup map for markets by condition ID
    const marketLookup = new Map<string, PolyGammaMarket>();
    for (const market of markets) {
      marketLookup.set(market.conditionId, market);
    }

    //Enhance orders with market information
    const enhancedOrders: OrderWithMarketInfo[] = openOrders.map(order => {
      const market = marketLookup.get(order.market);
      const sideText = `${order.side} ${order.outcome}`;

      //Handle case where market might not have events array or it might be empty
      const event = market?.events && market.events.length > 0 ? market.events[0] : null;

      return {
        ...order,
        marketTitle: market?.groupItemTitle,
        eventTitle: event?.title || market?.question || 'Unknown Event',
        eventIcon: event?.icon || market?.icon,
        eventId: event?.id || market?.id || 'unknown',
        sideText
      };
    });

    orders.value = enhancedOrders;
  }
  finally {
    isLoading.value = false;
  }
}

function onClickSort(column: string) {
  if (sortBy.value === column) {
    sortDesc.value = !sortDesc.value;
  }
  else {
    sortBy.value = column;
    sortDesc.value = true; //Default to descending
  }
}

function getSortClass(column: string) {
  return {
    "sort-active": sortBy.value === column,
    "cursor-pointer": true
  };
}

function getSortIcon(column: string) {
  if (sortBy.value !== column) return "unfold_more";
  return sortDesc.value ? "keyboard_arrow_down" : "keyboard_arrow_up";
}

async function onClickCancelOrder(order: OrderWithMarketInfo) {
  try {
    const res = await orderStore.cancelOrder([order.id]);

    if (res.canceled.length) {
      notifier.success(`${order.side} Order @ ${formatCents(Number(order.price))} CANCELED`);
      //Reload orders to reflect the cancellation
      await loadOrders();
    }
    if (Object.keys(res.not_canceled).length) {
      const notCanceled = Object.values(res.not_canceled);
      notifier.error(`Cancel failed: ${notCanceled.join(', ')}`);
    }
  }
  catch (error) {
    console.error('Error canceling order:', error);
  }
}

//Watch for userProxyWallet changes to reload orders
watch(() => props.userProxyWallet, () => {
  loadOrders();
}, { immediate: true });

onMounted(() => {
  loadOrders();
});
</script>

<style scoped lang="scss">
.orders-interface {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.orders-header {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e0e0e0;
  font-weight: 600;
  font-size: 12px;
  color: #666;
}

.header-spacer {
  width: var(--header-spacer-width, 300px);
}

.header-size {
  width: var(--header-size-width, 120px);
  display: flex;
  align-items: center;
  gap: 4px;
}

.header-price {
  width: var(--header-price-width, 80px);
  display: flex;
  align-items: center;
  gap: 4px;
}

.price-symbol {
  font-size: 14px;
  font-weight: 700;
}

.header-action {
  width: var(--header-action-width, 40px);
}

.sort-active {
  color: #1976d2;
}

.loading-container {
  display: flex;
  justify-content: center;
  padding: 40px;
}

.orders-container {
  display: flex;
  flex-direction: column;
}

.order-group {
  border-bottom: 1px solid #f0f0f0;
}

.event-container {
  display: flex;
  flex-direction: row;
  padding: 12px 0;
  gap: 12px;
}

.event-icon {
  flex-shrink: 0;
}

.data-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 8px;
}

.event-title {
  font-weight: 700;
  font-size: 14px;
  color: #333;
}

.order-row {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #666;
}

.cell-title {
  width: var(--cell-title-width, 150px);
  flex-shrink: 0;
}

.cell-side {
  width: var(--cell-side-width, 80px);
  flex-shrink: 0;
  font-weight: 600;
}

.cell-filled {
  width: var(--cell-filled-width, 50px);
  flex-shrink: 0;
  text-align: right;
}

.cell-separator {
  width: var(--cell-separator-width, 10px);
  flex-shrink: 0;
  text-align: center;
}

.cell-amount {
  width: var(--cell-amount-width, 50px);
  flex-shrink: 0;
  text-align: right;
}

.cell-at {
  width: var(--cell-at-width, 15px);
  flex-shrink: 0;
  text-align: center;
  margin: 0 4px;
}

.cell-price {
  width: var(--cell-price-width, 60px);
  flex-shrink: 0;
  text-align: right;
  font-weight: 600;
}

.cell-action {
  width: var(--cell-action-width, 40px);
  flex-shrink: 0;
  display: flex;
  justify-content: center;
}
</style>
