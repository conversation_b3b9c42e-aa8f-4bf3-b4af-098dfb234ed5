<template>
  <div class="orders-interface">
    <!-- Header row with sorting -->
    <div class="orders-header">
      <div class="header-spacer"></div>
      <div class="header-size" @click="onClickSort('size')" :class="getSortClass('size')">
        SIZE
        <q-icon :name="getSortIcon('size')" size="xs" />
      </div>
      <div class="header-price" @click="onClickSort('price')" :class="getSortClass('price')">
        <span class="price-symbol">¢</span>
        <q-icon :name="getSortIcon('price')" size="xs" />
      </div>
      <div class="header-action"></div>
    </div>

    <!-- Loading state -->
    <div v-if="isLoading" class="loading-container">
      <q-spinner size="md" />
    </div>

    <!-- Order groups -->
    <div v-else class="orders-container">
      <div v-for="group in sortedOrderGroups" :key="group.eventId" class="order-group">
        <!-- Event container: flex row with icon and data -->
        <div class="event-container">
          <!-- Event icon (left side) -->
          <div class="event-icon">
            <img :src="group.eventIcon || '/img/default_event.webp'" :alt="group.eventTitle" />
          </div>

          <!-- Data container: flex column with title and order rows -->
          <div class="data-container">
            <!-- Event title -->
            <div class="event-title">
              {{ group.eventTitle }}
            </div>

            <!-- Market groups -->
            <div v-for="market in group.markets" :key="`${group.eventId}-${market.marketTitle}`" class="market-group">
              <!-- Market title (only if not blank) -->
              <div v-if="market.marketTitle && market.marketTitle !== 'Unknown Market'" class="cell-title">{{ market.marketTitle }}</div>

              <!-- Order rows for this market -->
              <div v-for="order in market.orders" :key="order.id" class="order-container">
                <div class="order-row">
                  <div class="cell-side">{{ order.sideText }}</div>
                  <div class="cell-filled">{{ formatDecimal(Number(order.size_matched), 0) }}</div>
                  <div class="cell-separator">/</div>
                  <div class="cell-amount">{{ formatDecimal(Number(order.original_size), 0) }}</div>
                  <div class="cell-at">@</div>
                  <div class="cell-price">{{ formatCents(Number(order.price), 1) }}</div>
                  <div class="cell-action">
                    <q-btn size="xs" flat round class="text-negative" title="Cancel order"
                      @click="onClickCancelOrder(order)"
                    >
                      <template v-slot:default>
                        <q-icon name="close" class="cancel-icon" />
                      </template>
                    </q-btn>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { PolyClobOpenOrder, PolyGammaMarket } from "@shared/api-dataclasses-shared";
import { formatCents, formatDecimal } from "src/utils";
import { useApi } from "src/api";
import { useOrderStore } from "src/stores/order-store";
import { useNotifier } from "src/notifier";

interface OrderWithMarketInfo extends PolyClobOpenOrder {
  marketTitle?: string;
  eventTitle?: string;
  eventIcon?: string;
  eventId?: string;
  sideText: string;
}

interface MarketGroup {
  marketTitle: string;
  orders: OrderWithMarketInfo[];
}

interface OrderGroup {
  eventId: string;
  eventTitle: string;
  eventIcon?: string;
  markets: MarketGroup[];
  totalSize: number;
  highestPrice: number;
}

const props = defineProps<{
  userProxyWallet: string;
}>();

const api = useApi();
const orderStore = useOrderStore();
const notifier = useNotifier();

const isLoading = ref(false);
const orders = ref<OrderWithMarketInfo[]>([]);
const sortBy = ref("size");
const sortDesc = ref(true);
const dynamicWidths = ref({
  side: 80,
  filled: 50,
  amount: 50
});

const sortedOrderGroups = computed(() => {
  if (!orders.value.length) return [];

  //Group orders by event
  const groupsMap = new Map<string, OrderGroup>();

  for (const order of orders.value) {
    const eventId = order.eventId || 'unknown';

    if (!groupsMap.has(eventId)) {
      groupsMap.set(eventId, {
        eventId,
        eventTitle: order.eventTitle || 'Unknown Event',
        eventIcon: order.eventIcon,
        markets: [],
        totalSize: 0,
        highestPrice: 0
      });
    }

    const group = groupsMap.get(eventId)!;
    group.totalSize += Number(order.original_size) || 0;
    group.highestPrice = Math.max(group.highestPrice, Number(order.price) || 0);

    //Find or create market group within this event
    const marketTitle = order.marketTitle || 'Unknown Market';
    let marketGroup = group.markets.find(m => m.marketTitle === marketTitle);

    if (!marketGroup) {
      marketGroup = {
        marketTitle,
        orders: []
      };
      group.markets.push(marketGroup);
    }

    marketGroup.orders.push(order);
  }

  //Convert to array and sort
  const groups = Array.from(groupsMap.values());

  return groups.sort((a, b) => {
    let aVal: number, bVal: number;

    if (sortBy.value === "size") {
      aVal = a.totalSize;
      bVal = b.totalSize;
    }
    else { //price
      aVal = a.highestPrice;
      bVal = b.highestPrice;
    }

    if (aVal < bVal) return sortDesc.value ? 1 : -1;
    if (aVal > bVal) return sortDesc.value ? -1 : 1;
    return 0;
  });
});

async function loadOrders() {
  if (!props.userProxyWallet) return;

  isLoading.value = true;

  try {
    //Get open orders
    const openOrders = await api.getOrders();

    if (!openOrders.length) {
      orders.value = [];
      return;
    }

    //Get unique condition IDs for market lookup
    const conditionIds = [...new Set(openOrders.map(order => order.market))];

    //Get market data for all condition IDs
    const markets = await api.getMarkets(undefined, conditionIds);

    //Create lookup map for markets by condition ID
    const marketLookup = new Map<string, PolyGammaMarket>();
    for (const market of markets) {
      marketLookup.set(market.conditionId, market);
    }

    //Enhance orders with market information
    const enhancedOrders: OrderWithMarketInfo[] = openOrders.map(order => {
      const market = marketLookup.get(order.market);
      const sideText = `${order.side} ${order.outcome}`;

      //Handle case where market might not have events array or it might be empty
      const event = market?.events && market.events.length > 0 ? market.events[0] : null;

      return {
        ...order,
        marketTitle: market?.groupItemTitle,
        eventTitle: event?.title || market?.question || 'Unknown Event',
        eventIcon: event?.icon || market?.icon,
        eventId: event?.id || market?.id || 'unknown',
        sideText
      };
    });

    orders.value = enhancedOrders;

    //Calculate dynamic widths after data is loaded
    calculateDynamicWidths();
  }
  finally {
    isLoading.value = false;
  }
}

function onClickSort(column: string) {
  if (sortBy.value === column) {
    sortDesc.value = !sortDesc.value;
  }
  else {
    sortBy.value = column;
    sortDesc.value = true; //Default to descending
  }
}

function getSortClass(column: string) {
  return {
    "sort-active": sortBy.value === column,
    "cursor-pointer": true
  };
}

function getSortIcon(column: string) {
  if (sortBy.value !== column) return "unfold_more";
  return sortDesc.value ? "keyboard_arrow_down" : "keyboard_arrow_up";
}

function measureTextWidth(text: string): number {
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');
  if (!context) return 0;

  //Match the font used in the cells
  context.font = '13px sans-serif';
  return context.measureText(text).width;
}

function calculateDynamicWidths() {
  if (!orders.value.length) return;

  const sidePadding = 0;
  const filledPadding = 0;
  const amountPadding = 0;

  //Get all orders from all markets in all groups
  const allOrders: OrderWithMarketInfo[] = [];
  for (const group of sortedOrderGroups.value) {
    for (const market of group.markets) {
      allOrders.push(...market.orders);
    }
  }

  const sideWidths = allOrders.map(order => measureTextWidth(order.sideText));
  const filledWidths = allOrders.map(order => measureTextWidth(formatDecimal(Number(order.size_matched), 0)));
  const amountWidths = allOrders.map(order => measureTextWidth(formatDecimal(Number(order.original_size), 0)));

  dynamicWidths.value = {
    side: Math.max(...sideWidths, 20) + sidePadding,
    filled: Math.max(...filledWidths, 20) + filledPadding,
    amount: Math.max(...amountWidths, 20) + amountPadding
  };

  //Apply to CSS custom properties
  document.documentElement.style.setProperty('--cell-side-width', `${dynamicWidths.value.side}px`);
  document.documentElement.style.setProperty('--cell-filled-width', `${dynamicWidths.value.filled}px`);
  document.documentElement.style.setProperty('--cell-amount-width', `${dynamicWidths.value.amount}px`);

  //Update header size width (filled + separator + amount)
  const headerSizeWidth = dynamicWidths.value.filled + 10 + dynamicWidths.value.amount; //10px for separator
  document.documentElement.style.setProperty('--header-size-width', `${headerSizeWidth}px`);
}

async function onClickCancelOrder(order: OrderWithMarketInfo) {
  try {
    const res = await orderStore.cancelOrder([order.id]);

    if (res.canceled.length) {
      notifier.success(`${order.side} Order @ ${formatCents(Number(order.price))} CANCELED`);
      //Reload orders to reflect the cancellation
      await loadOrders();
    }
    if (Object.keys(res.not_canceled).length) {
      const notCanceled = Object.values(res.not_canceled);
      notifier.error(`Cancel failed: ${notCanceled.join(', ')}`);
    }
  }
  catch (error) {
    console.error('Error canceling order:', error);
  }
}

//Watch for userProxyWallet changes to reload orders
watch(() => props.userProxyWallet, () => {
  loadOrders();
}, { immediate: true });

//Watch for orders changes to recalculate widths
watch(orders, () => {
  calculateDynamicWidths();
}, { deep: true });

onMounted(() => {
  loadOrders();
});
</script>

<style scoped lang="scss">
.orders-interface {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.orders-header {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e0e0e0;
  font-weight: 600;
  font-size: 12px;
  color: #666;
}

.header-spacer {
  width: var(--header-spacer-width, 300px);
}

.header-size {
  width: var(--header-size-width, 120px);
  display: flex;
  align-items: center;
  gap: 4px;
}

.header-price {
  width: var(--header-price-width, 80px);
  display: flex;
  align-items: center;
  gap: 4px;
}

.price-symbol {
  font-size: 14px;
  font-weight: 700;
}

.header-action {
  width: var(--header-action-width, 40px);
}

.sort-active {
  color: #1976d2;
}

.loading-container {
  display: flex;
  justify-content: center;
  padding: 40px;
}

.orders-container {
  display: flex;
  flex-direction: column;
}

.order-group {
  border-bottom: 1px solid #f0f0f0;
}

.event-container {
  display: flex;
  flex-direction: row;
  padding: 6px 0;
}

.event-icon {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  padding-top: 4px;
  margin-right: 6px;

  img {
    width: 42px;
    height: 42px;
    border-radius: 4px;
    object-fit: cover;
  }
}

.data-container {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.event-title {
  font-weight: 700;
  font-size: 14px;
  color: #333;
}

.market-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 8px;
}

.order-container {
  display: flex;
  flex-direction: column;
}

.order-row {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #666;
}

.cell-title {
  font-size: 12px;
  color: #888;
  font-weight: 500;
}

.cell-side {
  width: var(--cell-side-width, 80px);
  flex-shrink: 0;
  font-weight: 600;
}

.cell-filled {
  width: var(--cell-filled-width, 50px);
  flex-shrink: 0;
  text-align: right;
}

.cell-separator {
  width: 10px;
  flex-shrink: 0;
  text-align: center;
}

.cell-amount {
  width: var(--cell-amount-width, 50px);
  flex-shrink: 0;
  text-align: right;
}

.cell-at {
  width: 15px;
  flex-shrink: 0;
  text-align: center;
  margin-left: 4px;
  margin-right: 2px;
}

.cell-price {
  width: var(--cell-price-width, 35px);
  flex-shrink: 0;
  text-align: right;
  font-weight: 600;
}

.cell-action {
  width: var(--cell-action-width, 40px);
  flex-shrink: 0;
  display: flex;
  justify-content: center;
}

.cancel-icon {
  margin-top: 4px;
}
</style>
